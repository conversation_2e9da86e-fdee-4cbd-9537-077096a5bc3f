package com.sportal365.articlescheduler.application.service.schedules;

import com.sportal365.articlescheduler.application.dto.schedule.request.ScheduleCreateRequest;
import com.sportal365.articlescheduler.application.dto.schedule.request.ScheduleListRequest;
import com.sportal365.articlescheduler.application.dto.schedule.request.ScheduleMatchRequest;
import com.sportal365.articlescheduler.application.dto.schedule.request.ScheduleUpdateRequest;
import com.sportal365.articlescheduler.application.dto.schedule.response.ScheduleResponse;
import com.sportal365.articlescheduler.application.service.article.generation.AsyncArticleGenerationService;
import com.sportal365.articlescheduler.domain.model.Schedule;
import com.sportal365.articlescheduler.domain.model.enums.ScheduleStatus;
import com.sportal365.articlescheduler.domain.model.enums.ScheduleType;
import com.sportal365.articlescheduler.domain.model.mappers.ScheduleMapper;
import com.sportal365.articlescheduler.domain.utils.ProjectUtils;
import com.sportal365.common.enums.SportEnum;
import com.sportal365.configurationclient.model.Project;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("ScheduleService Tests")
class ScheduleServiceTest {

    @Mock
    private SchedulePersistenceService schedulePersistenceService;

    @Mock
    private ScheduleMapper scheduleMapper;

    @Mock
    private AsyncArticleGenerationService asyncArticleGenerationService;

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private ProjectUtils projectUtils;

    @Mock
    private Executor taskExecutor;

    @InjectMocks
    private ScheduleService scheduleService;

    private Project mockProject;
    private ScheduleCreateRequest testRequest;
    private Schedule testSchedule;
    private ScheduleResponse testResponse;

    @BeforeEach
    void setUp() {
        setupTestData();
    }

    @Nested
    @DisplayName("Create Schedule Tests")
    class CreateScheduleTests {

        @Test
        @DisplayName("Should create schedules with IMMEDIATELY type and process them immediately")
        void shouldCreateSchedulesWithImmediatelyTypeAndProcessThem() {
            // Arrange
            setupMockProject();
            testRequest = createRequestWithScheduleType(ScheduleType.IMMEDIATELY);
            List<Schedule> savedSchedules = List.of(testSchedule);
            List<ScheduleResponse> expectedResponses = List.of(testResponse);

            when(projectUtils.getProject("test-domain")).thenReturn(mockProject);
            when(schedulePersistenceService.saveSchedule(any(Schedule.class))).thenReturn(testSchedule);
            when(scheduleMapper.toResponseList(savedSchedules)).thenReturn(expectedResponses);
            when(schedulePersistenceService.saveSchedules(anyList())).thenReturn(savedSchedules);

            // Act
            List<ScheduleResponse> result = scheduleService.createSchedule(testRequest, "test-domain");

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            verify(schedulePersistenceService).saveSchedule(any(Schedule.class));
            verify(schedulePersistenceService).saveSchedules(anyList()); // For status update to INPROGRESS
            verify(taskExecutor).execute(any(Runnable.class)); // Async processing
        }

        @Test
        @DisplayName("Should create schedules with SCHEDULED type and filter for immediate processing")
        void shouldCreateSchedulesWithScheduledTypeAndFilterForImmediateProcessing() {
            // Arrange
            setupMockProject();
            testRequest = createRequestWithScheduleType(ScheduleType.SCHEDULED);
            List<Schedule> savedSchedules = List.of(testSchedule);
            List<ScheduleResponse> expectedResponses = List.of(testResponse);

            when(projectUtils.getProject("test-domain")).thenReturn(mockProject);
            when(schedulePersistenceService.saveSchedule(any(Schedule.class))).thenReturn(testSchedule);
            when(scheduleMapper.toResponseList(savedSchedules)).thenReturn(expectedResponses);
            when(schedulePersistenceService.saveSchedules(anyList())).thenReturn(new ArrayList<>());

            // Act
            List<ScheduleResponse> result = scheduleService.createSchedule(testRequest, "test-domain");

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            verify(schedulePersistenceService).saveSchedule(any(Schedule.class));
            verify(schedulePersistenceService).saveSchedules(anyList()); // For filtering immediate processing
        }

        @Test
        @DisplayName("Should use default schedule type when not provided")
        void shouldUseDefaultScheduleTypeWhenNotProvided() {
            // Arrange
            setupMockProject();
            testRequest = createRequestWithoutScheduleType();
            List<Schedule> savedSchedules = List.of(testSchedule);
            List<ScheduleResponse> expectedResponses = List.of(testResponse);

            when(projectUtils.getProject("test-domain")).thenReturn(mockProject);
            when(schedulePersistenceService.saveSchedule(any(Schedule.class))).thenReturn(testSchedule);
            when(scheduleMapper.toResponseList(savedSchedules)).thenReturn(expectedResponses);
            when(schedulePersistenceService.saveSchedules(anyList())).thenReturn(savedSchedules);

            // Act
            List<ScheduleResponse> result = scheduleService.createSchedule(testRequest, "test-domain");

            // Assert
            assertNotNull(result);
            verify(schedulePersistenceService).saveSchedule(argThat(schedule -> 
                schedule.getScheduleType() == ScheduleType.getDefault()));
        }

        @Test
        @DisplayName("Should handle DataIntegrityViolationException gracefully during schedule creation")
        void shouldHandleDataIntegrityViolationExceptionGracefully() {
            // Arrange
            setupMockProject();
            testRequest = createRequestWithScheduleType(ScheduleType.IMMEDIATELY);
            List<ScheduleResponse> expectedResponses = List.of();

            when(projectUtils.getProject("test-domain")).thenReturn(mockProject);
            when(schedulePersistenceService.saveSchedule(any(Schedule.class)))
                .thenThrow(new DataIntegrityViolationException("Duplicate key"));
            when(scheduleMapper.toResponseList(anyList())).thenReturn(expectedResponses);

            // Act
            List<ScheduleResponse> result = scheduleService.createSchedule(testRequest, "test-domain");

            // Assert
            assertNotNull(result);
            assertEquals(0, result.size());
            verify(schedulePersistenceService).saveSchedule(any(Schedule.class));
        }

        @Test
        @DisplayName("Should create multiple schedules from multiple matches")
        void shouldCreateMultipleSchedulesFromMultipleMatches() {
            // Arrange
            setupMockProject();
            testRequest = createRequestWithMultipleMatches();
            List<Schedule> savedSchedules = List.of(testSchedule, testSchedule);
            List<ScheduleResponse> expectedResponses = List.of(testResponse, testResponse);

            when(projectUtils.getProject("test-domain")).thenReturn(mockProject);
            when(schedulePersistenceService.saveSchedule(any(Schedule.class))).thenReturn(testSchedule);
            when(scheduleMapper.toResponseList(savedSchedules)).thenReturn(expectedResponses);
            when(schedulePersistenceService.saveSchedules(anyList())).thenReturn(savedSchedules);

            // Act
            List<ScheduleResponse> result = scheduleService.createSchedule(testRequest, "test-domain");

            // Assert
            assertNotNull(result);
            assertEquals(2, result.size());
            verify(schedulePersistenceService, times(2)).saveSchedule(any(Schedule.class));
        }
    }

    @Nested
    @DisplayName("Schedule Management Tests")
    class ScheduleManagementTests {

        @Test
        @DisplayName("Should get schedule by id and project domain")
        void shouldGetScheduleByIdAndProjectDomain() {
            // Arrange
            when(schedulePersistenceService.getScheduleById("test-id", "test-domain"))
                .thenReturn(testSchedule);

            // Act
            Schedule result = scheduleService.getSchedule("test-id", "test-domain");

            // Assert
            assertNotNull(result);
            assertEquals(testSchedule, result);
            verify(schedulePersistenceService).getScheduleById("test-id", "test-domain");
        }

        @Test
        @DisplayName("Should update schedule generation time")
        void shouldUpdateScheduleGenerationTime() {
            // Arrange
            Instant newGenerationTime = Instant.now().plusSeconds(3600);
            ScheduleUpdateRequest updateRequest = ScheduleUpdateRequest.builder()
                .generationTime(newGenerationTime)
                .build();

            when(schedulePersistenceService.getScheduleById("test-id", "test-domain"))
                .thenReturn(testSchedule);
            when(schedulePersistenceService.saveSchedule(testSchedule)).thenReturn(testSchedule);

            // Act
            Schedule result = scheduleService.updateSchedule("test-id", updateRequest, "test-domain");

            // Assert
            assertNotNull(result);
            assertEquals(newGenerationTime, testSchedule.getGenerationTime());
            verify(schedulePersistenceService).saveSchedule(testSchedule);
        }

        @Test
        @DisplayName("Should delete schedule")
        void shouldDeleteSchedule() {
            // Arrange
            when(schedulePersistenceService.getScheduleById("test-id", "test-domain"))
                .thenReturn(testSchedule);

            // Act
            scheduleService.deleteSchedule("test-id", "test-domain");

            // Assert
            verify(schedulePersistenceService).deleteSchedule(testSchedule);
        }

        @Test
        @DisplayName("Should count active schedules")
        void shouldCountActiveSchedules() {
            // Arrange
            when(schedulePersistenceService.countActiveSchedules("test-domain")).thenReturn(5);

            // Act
            Integer result = scheduleService.schedulesCount("test-domain");

            // Assert
            assertEquals(5, result);
            verify(schedulePersistenceService).countActiveSchedules("test-domain");
        }
    }

    @Nested
    @DisplayName("List Schedules Tests")
    class ListSchedulesTests {

        @Test
        @DisplayName("Should list schedules with pagination")
        void shouldListSchedulesWithPagination() {
            // Arrange
            ScheduleListRequest listRequest = ScheduleListRequest.builder()
                .pageRequest(com.sportal365.articlescheduler.application.dto.common.PageRequest.builder()
                    .page(0)
                    .size(10)
                    .build())
                .build();

            List<Schedule> schedules = List.of(testSchedule);
            Page<Schedule> expectedPage = new PageImpl<>(schedules);

            when(mongoTemplate.find(any(Query.class), eq(Schedule.class))).thenReturn(schedules);
            when(mongoTemplate.count(any(Query.class), eq(Schedule.class))).thenReturn(1L);

            // Act
            Page<Schedule> result = scheduleService.listSchedules("test-domain", listRequest);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getTotalElements());
            assertEquals(1, result.getContent().size());
            verify(mongoTemplate).find(any(Query.class), eq(Schedule.class));
            verify(mongoTemplate).count(any(Query.class), eq(Schedule.class));
        }
    }

    @Nested
    @DisplayName("Schedule Processing Logic Tests")
    class ScheduleProcessingLogicTests {

        @Test
        @DisplayName("Should filter schedules for immediate processing based on match date and timezone")
        void shouldFilterSchedulesForImmediateProcessingBasedOnMatchDateAndTimezone() {
            // Arrange
            Instant todayMatch = ZonedDateTime.now(ZoneId.of("UTC"))
                .plusHours(2).toInstant();
            Schedule todaySchedule = createScheduleWithMatchDate(todayMatch, "UTC");
            
            Instant tomorrowMatch = ZonedDateTime.now(ZoneId.of("UTC"))
                .plusDays(1).toInstant();
            Schedule tomorrowSchedule = createScheduleWithMatchDate(tomorrowMatch, "UTC");

            List<Schedule> inputSchedules = List.of(todaySchedule, tomorrowSchedule);
            List<Schedule> expectedFiltered = List.of(todaySchedule);

            when(schedulePersistenceService.saveSchedules(anyList())).thenReturn(expectedFiltered);

            // Act
            List<Schedule> result = scheduleService.filterSchedulesForImmediateProcessing(inputSchedules);

            // Assert
            assertEquals(1, result.size());
            assertEquals(ScheduleStatus.INPROGRESS, result.get(0).getStatus());
            verify(schedulePersistenceService).saveSchedules(anyList());
        }

        @Test
        @DisplayName("Should process immediate schedules asynchronously")
        void shouldProcessImmediateSchedulesAsynchronously() {
            // Arrange
            List<Schedule> schedules = List.of(testSchedule);

            // Act
            scheduleService.processImmediateSchedulesAsync(schedules);

            // Assert
            verify(taskExecutor).execute(any(Runnable.class));
        }

        @Test
        @DisplayName("Should not filter schedules when match date is not today")
        void shouldNotFilterSchedulesWhenMatchDateIsNotToday() {
            // Arrange
            Instant tomorrowMatch = ZonedDateTime.now(ZoneId.of("UTC"))
                .plusDays(1).toInstant();
            Schedule tomorrowSchedule = createScheduleWithMatchDate(tomorrowMatch, "UTC");

            List<Schedule> inputSchedules = List.of(tomorrowSchedule);
            when(schedulePersistenceService.saveSchedules(anyList())).thenReturn(List.of());

            // Act
            List<Schedule> result = scheduleService.filterSchedulesForImmediateProcessing(inputSchedules);

            // Assert
            assertEquals(0, result.size());
            verify(schedulePersistenceService).saveSchedules(anyList());
        }

        @Test
        @DisplayName("Should handle empty schedule list for immediate processing")
        void shouldHandleEmptyScheduleListForImmediateProcessing() {
            // Arrange
            List<Schedule> emptySchedules = List.of();
            when(schedulePersistenceService.saveSchedules(anyList())).thenReturn(emptySchedules);

            // Act
            List<Schedule> result = scheduleService.filterSchedulesForImmediateProcessing(emptySchedules);

            // Assert
            assertEquals(0, result.size());
            verify(schedulePersistenceService).saveSchedules(anyList());
        }
    }

    // Helper methods for creating test data
    private void setupMockProject() {
        // Mock the nested Project structure
        Project.Configuration.Languages.Language defaultLanguage = mock(Project.Configuration.Languages.Language.class);
        when(defaultLanguage.getLanguageCode()).thenReturn("en");

        Project.Configuration.Languages languages = mock(Project.Configuration.Languages.class);
        when(languages.getDefaultLanguage()).thenReturn(defaultLanguage);

        Project.Configuration.Services.AiArticleGenerationService.ProviderProperties providerProperties =
            mock(Project.Configuration.Services.AiArticleGenerationService.ProviderProperties.class);
        when(providerProperties.getProvider()).thenReturn("test-provider");
        when(providerProperties.getLlmModel()).thenReturn("test-model");

        Project.Configuration.Services.AiArticleGenerationService aiService =
            mock(Project.Configuration.Services.AiArticleGenerationService.class);
        when(aiService.getTemperature()).thenReturn("0.7");
        when(aiService.getProviderProperties()).thenReturn(providerProperties);

        Project.Configuration.Services services = mock(Project.Configuration.Services.class);
        when(services.getAiArticleGenerationService()).thenReturn(aiService);

        Project.Configuration configuration = mock(Project.Configuration.class);
        when(configuration.getTimezone()).thenReturn("UTC");
        when(configuration.getLanguages()).thenReturn(languages);
        when(configuration.getServices()).thenReturn(services);

        mockProject = mock(Project.class);
        when(mockProject.getConfiguration()).thenReturn(configuration);
    }

    private void setupTestData() {
        testSchedule = Schedule.builder()
            .id("test-id")
            .templateName("Test Template")
            .matchDetails(Schedule.MatchDetails.builder()
                .matchId("match-1")
                .matchName("Team A - Team B")
                .competitionId("comp-1")
                .competitionName("Test Competition")
                .matchDate(Instant.now().plusSeconds(3600))
                .build())
            .generationTime(Instant.now().plusSeconds(1800))
            .status(ScheduleStatus.SCHEDULED)
            .projectDomain("test-domain")
            .timeZone("UTC")
            .generateSummary(true)
            .generateStrapline(true)
            .category(Schedule.Category.builder().id("cat-1").name("Test Category").build())
            .userId("user-1")
            .sport(SportEnum.FOOTBALL)
            .scheduleType(ScheduleType.IMMEDIATELY)
            .build();

        testResponse = ScheduleResponse.builder()
            .id("test-id")
            .matchName("Team A - Team B")
            .competitionName("Test Competition")
            .status(ScheduleStatus.SCHEDULED)
            .templateName("Test Template")
            .build();
    }

    private ScheduleCreateRequest createRequestWithScheduleType(ScheduleType scheduleType) {
        return ScheduleCreateRequest.builder()
            .templateName("Test Template")
            .templateType("PRE_GAME")
            .category("cat-1")
            .categoryName("Test Category")
            .userId("user-1")
            .userName("Test User")
            .sport("FOOTBALL")
            .generateSummary(true)
            .generateStrapline(true)
            .scheduleType(scheduleType)
            .matches(List.of(createTestMatchRequest()))
            .build();
    }

    private ScheduleCreateRequest createRequestWithoutScheduleType() {
        return ScheduleCreateRequest.builder()
            .templateName("Test Template")
            .templateType("PRE_GAME")
            .category("cat-1")
            .categoryName("Test Category")
            .userId("user-1")
            .userName("Test User")
            .sport("FOOTBALL")
            .generateSummary(true)
            .generateStrapline(true)
            // No scheduleType - should default to IMMEDIATELY
            .matches(List.of(createTestMatchRequest()))
            .build();
    }

    private ScheduleCreateRequest createRequestWithMultipleMatches() {
        return ScheduleCreateRequest.builder()
            .templateName("Test Template")
            .templateType("PRE_GAME")
            .category("cat-1")
            .categoryName("Test Category")
            .userId("user-1")
            .userName("Test User")
            .sport("FOOTBALL")
            .generateSummary(true)
            .generateStrapline(true)
            .scheduleType(ScheduleType.IMMEDIATELY)
            .matches(List.of(
                createTestMatchRequest(),
                createSecondTestMatchRequest()
            ))
            .build();
    }

    private ScheduleMatchRequest createTestMatchRequest() {
        return ScheduleMatchRequest.builder()
            .matchId("match-1")
            .competitionId("comp-1")
            .competitionName("Test Competition")
            .matchName("Team A - Team B")
            .matchDate(Instant.now().plusSeconds(3600))
            .build();
    }

    private ScheduleMatchRequest createSecondTestMatchRequest() {
        return ScheduleMatchRequest.builder()
            .matchId("match-2")
            .competitionId("comp-1")
            .competitionName("Test Competition")
            .matchName("Team C - Team D")
            .matchDate(Instant.now().plusSeconds(7200))
            .build();
    }

    private Schedule createScheduleWithMatchDate(Instant matchDate, String timeZone) {
        return Schedule.builder()
            .id("schedule-" + System.currentTimeMillis())
            .matchDetails(Schedule.MatchDetails.builder()
                .matchId("match-" + System.currentTimeMillis())
                .matchName("Test Match")
                .matchDate(matchDate)
                .build())
            .timeZone(timeZone)
            .status(ScheduleStatus.SCHEDULED)
            .build();
    }
}
